2025-07-22 21:40:42.535 [MessageBroker-13] ERROR PERFORMANCE - 
                [hourlyPerformanceReport] [0ms] [] FAILED_SERVICE_METHOD: StatisticsMonitoringService.getMonitoringStats failed after 0ms with error: NullPointerException
2025-07-22 21:40:42.932 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [267ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 267ms
2025-07-22 21:40:42.932 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [267ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 267ms
2025-07-22 21:40:42.932 [MessageBroker-2] INFO  PERFORMANCE - 
                [sendVerificationReminders] [319ms] [] DB_OPERATION: $Proxy194.findUnverifiedUsersInTimeR<PERSON><PERSON> took 319ms
2025-07-22 21:40:42.932 [MessageBroker-15] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [319ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 319ms
2025-07-22 21:40:42.943 [main] INFO  PERFORMANCE - 
                [] [178ms] [] DB_OPERATION: $Proxy211.count took 178ms
2025-07-22 21:40:43.120 [ForkJoinPool.commonPool-worker-1] INFO  PERFORMANCE - 
                [] [101ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 101ms
2025-07-22 21:40:43.120 [ForkJoinPool.commonPool-worker-2] INFO  PERFORMANCE - 
                [] [105ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 105ms
2025-07-22 21:40:43.225 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [582ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 582ms
2025-07-22 21:40:43.225 [MessageBroker-12] INFO  PERFORMANCE - 
                [cacheWarming] [596ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 596ms
2025-07-22 21:40:43.225 [MessageBroker-14] INFO  PERFORMANCE - 
                [periodicHealthCheck] [690ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 690ms
2025-07-22 21:40:43.225 [MessageBroker-12] INFO  PERFORMANCE - 
                [cacheWarming] [690ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.cacheWarming took 690ms
2025-07-22 22:53:11.840 [MessageBroker-13] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [441ms] [] DB_OPERATION: $Proxy194.findBySubscriptionStatusAndSubscriptionEndDateBefore took 441ms
2025-07-22 22:53:11.953 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [201ms] [] DB_OPERATION: $Proxy194.countActiveSubscriptionUsers took 201ms
2025-07-22 22:53:12.011 [MessageBroker-13] INFO  PERFORMANCE - 
                [updateExpiredSubscriptions] [626ms] [] SERVICE_METHOD: SubscriptionSchedulerService.updateExpiredSubscriptions took 626ms
2025-07-22 22:53:12.161 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [208ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 208ms
2025-07-22 22:53:12.462 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [301ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 301ms
2025-07-22 22:53:12.720 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [258ms] [] DB_OPERATION: $Proxy209.countUsersWithImprovement took 258ms
2025-07-22 22:53:13.331 [ForkJoinPool.commonPool-worker-8] WARN  PERFORMANCE - 
                [] [609ms] [] SLOW_DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 609ms
2025-07-22 22:53:13.648 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [315ms] [] DB_OPERATION: $Proxy209.getTotalCardsStudied took 315ms
2025-07-22 22:53:14.614 [ForkJoinPool.commonPool-worker-8] WARN  PERFORMANCE - 
                [] [691ms] [] SLOW_DB_OPERATION: $Proxy207.countByDeckDeletedFalse took 691ms
2025-07-22 22:53:14.845 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [231ms] [] DB_OPERATION: $Proxy208.countDistinctTags took 231ms
2025-07-22 22:53:14.923 [MessageBroker-4] WARN  PERFORMANCE - 
                [periodicHealthCheck] [3524ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 3524ms
2025-07-22 22:53:14.936 [MessageBroker-4] WARN  PERFORMANCE - 
                [periodicHealthCheck] [3551ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 3551ms
2025-07-22 22:53:15.310 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [163ms] [] DB_OPERATION: $Proxy209.countUsersWithImprovement took 163ms
2025-07-22 22:53:15.752 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [394ms] [] DB_OPERATION: $Proxy209.getAverageStudyStreakDays took 394ms
2025-07-22 22:53:16.056 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [128ms] [] DB_OPERATION: $Proxy206.countByPublicTrueAndDeletedFalse took 128ms
2025-07-22 22:53:16.083 [MessageBroker-4] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1143ms] [] SLOW_SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 1143ms
2025-07-22 22:53:16.086 [MessageBroker-4] WARN  PERFORMANCE - 
                [periodicHealthCheck] [1150ms] [] SLOW_SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 1150ms
2025-07-22 22:53:16.244 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [139ms] [] DB_OPERATION: $Proxy194.countActiveUsersSince took 139ms
2025-07-22 22:53:16.583 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [199ms] [] DB_OPERATION: $Proxy209.getAverageSessionsPerWeek took 199ms
2025-07-22 22:53:17.017 [MessageBroker-4] INFO  PERFORMANCE - 
                [periodicHealthCheck] [927ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 927ms
2025-07-22 22:53:17.022 [MessageBroker-4] INFO  PERFORMANCE - 
                [periodicHealthCheck] [936ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 936ms
2025-07-22 22:53:17.411 [ForkJoinPool.commonPool-worker-8] INFO  PERFORMANCE - 
                [] [140ms] [] DB_OPERATION: $Proxy194.countUsersWithStudyDataSince took 140ms
2025-07-22 22:53:17.825 [MessageBroker-4] INFO  PERFORMANCE - 
                [periodicHealthCheck] [729ms] [] SERVICE_METHOD: PublicStatisticsService.getPublicStatistics took 729ms
2025-07-22 22:53:17.855 [MessageBroker-4] INFO  PERFORMANCE - 
                [periodicHealthCheck] [793ms] [] SERVICE_METHOD: StatisticsScheduledMonitor.periodicHealthCheck took 793ms
